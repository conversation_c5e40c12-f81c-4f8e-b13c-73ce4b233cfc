import { Link } from 'react-router-dom';
import { useState, useEffect, useRef } from 'react';
import { useTheme } from '../context/ThemeContext';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAboutDropdownOpen, setIsAboutDropdownOpen] = useState(false);

  const { isDarkMode, toggleDarkMode } = useTheme();
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside (desktop only)
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Only handle desktop dropdown - don't interfere with mobile menu
      if (window.innerWidth >= 768) { // md breakpoint
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsAboutDropdownOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <nav className="bg-white dark:bg-primary-900 shadow-lg transition-colors duration-200">
      <div className="w-full px-6">
        <div className="flex justify-between h-20">
          <div className="flex items-center">
            <Link to="/" className="text-xl font-display font-semibold text-primary-900 dark:text-white">
              Riverdale School of Music
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white">Home</Link>

            {/* About Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsAboutDropdownOpen(!isAboutDropdownOpen)}
                className="text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white flex items-center"
              >
                About
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {isAboutDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-primary-800 rounded-lg shadow-lg border border-gray-200 dark:border-primary-700 z-50">
                  <Link
                    to="/information"
                    className="block px-4 py-3 text-primary-600 dark:text-primary-200 hover:bg-gray-50 dark:hover:bg-primary-700 transition-colors duration-200 rounded-t-lg"
                    onClick={() => setIsAboutDropdownOpen(false)}
                  >
                    Information
                  </Link>
                  <Link
                    to="/studio-policies"
                    className="block px-4 py-3 text-primary-600 dark:text-primary-200 hover:bg-gray-50 dark:hover:bg-primary-700 transition-colors duration-200"
                    onClick={() => setIsAboutDropdownOpen(false)}
                  >
                    Studio Policies
                  </Link>
                  <Link
                    to="/studio-calendar"
                    className="block px-4 py-3 text-primary-600 dark:text-primary-200 hover:bg-gray-50 dark:hover:bg-primary-700 transition-colors duration-200 rounded-b-lg"
                    onClick={() => setIsAboutDropdownOpen(false)}
                  >
                    Studio Calendar
                  </Link>
                </div>
              )}
            </div>

            <Link to="/services" className="text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white">Lessons</Link>
            <Link to="/gallery" className="text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white">Gallery</Link>
            <Link to="/contact" className="text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white">Contact</Link>
            
            {/* Theme Toggle Button */}
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-lg bg-primary-100 dark:bg-primary-800 text-primary-600 dark:text-primary-200 hover:bg-primary-200 dark:hover:bg-primary-700 transition-colors duration-200"
              aria-label="Toggle dark mode"
            >
              {isDarkMode ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" 
                  />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" 
                  />
                </svg>
              )}
            </button>
          </div>

          {/* Mobile menu button and theme toggle */}
          <div className="md:hidden flex items-center space-x-2">
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-lg bg-primary-100 dark:bg-primary-800 text-primary-600 dark:text-primary-200 hover:bg-primary-200 dark:hover:bg-primary-700 transition-colors duration-200"
              aria-label="Toggle dark mode"
            >
              {isDarkMode ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" 
                  />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" 
                  />
                </svg>
              )}
            </button>
            
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white hover:bg-primary-100 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
            >
              <span className="sr-only">Open main menu</span>
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`md:hidden ${isMenuOpen ? 'block' : 'hidden'}`}>
        <div className="px-2 pt-2 pb-3 space-y-1 bg-white dark:bg-primary-900">
          <Link
            to="/"
            className="block px-3 py-2 rounded-md text-base font-medium text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white hover:bg-primary-50 dark:hover:bg-primary-800"
            onClick={() => setIsMenuOpen(false)}
          >
            Home
          </Link>
          {/* About Dropdown for Mobile */}
          <div>
            <button
              onClick={() => setIsAboutDropdownOpen(!isAboutDropdownOpen)}
              className="w-full text-left px-3 py-2 rounded-md text-base font-medium text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white hover:bg-primary-50 dark:hover:bg-primary-800 flex items-center justify-between"
            >
              About
              <svg className={`w-4 h-4 transition-transform ${isAboutDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {isAboutDropdownOpen && (
              <div className="pl-6 space-y-1 mt-1">
                <Link
                  to="/information"
                  className="block px-3 py-2 rounded-md text-sm font-medium text-primary-500 dark:text-primary-300 hover:text-primary-700 dark:hover:text-white hover:bg-primary-50 dark:hover:bg-primary-800"
                  onClick={() => {setIsAboutDropdownOpen(false); setIsMenuOpen(false);}}
                >
                  Information
                </Link>
                <Link
                  to="/studio-policies"
                  className="block px-3 py-2 rounded-md text-sm font-medium text-primary-500 dark:text-primary-300 hover:text-primary-700 dark:hover:text-white hover:bg-primary-50 dark:hover:bg-primary-800"
                  onClick={() => {setIsAboutDropdownOpen(false); setIsMenuOpen(false);}}
                >
                  Studio Policies
                </Link>
                <Link
                  to="/studio-calendar"
                  className="block px-3 py-2 rounded-md text-sm font-medium text-primary-500 dark:text-primary-300 hover:text-primary-700 dark:hover:text-white hover:bg-primary-50 dark:hover:bg-primary-800"
                  onClick={() => {setIsAboutDropdownOpen(false); setIsMenuOpen(false);}}
                >
                  Studio Calendar
                </Link>
              </div>
            )}
          </div>
          <Link
            to="/services"
            className="block px-3 py-2 rounded-md text-base font-medium text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white hover:bg-primary-50 dark:hover:bg-primary-800"
            onClick={() => setIsMenuOpen(false)}
          >
            Lessons
          </Link>
          <Link
            to="/gallery"
            className="block px-3 py-2 rounded-md text-base font-medium text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white hover:bg-primary-50 dark:hover:bg-primary-800"
            onClick={() => setIsMenuOpen(false)}
          >
            Gallery
          </Link>
          <Link
            to="/contact"
            className="block px-3 py-2 rounded-md text-base font-medium text-primary-600 dark:text-primary-200 hover:text-primary-800 dark:hover:text-white hover:bg-primary-50 dark:hover:bg-primary-800"
            onClick={() => setIsMenuOpen(false)}
          >
            Contact
          </Link>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;



