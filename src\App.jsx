import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './context/ThemeContext';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import Information from './pages/About';
import StudioPolicies from './pages/StudioPolicies';
import StudioCalendar from './pages/StudioCalendar';

import Services from './pages/Services';
import Gallery from './pages/Gallery';
import Contact from './pages/Contact';
import Footer from './components/Footer';

function App() {
  return (
    <ThemeProvider>
      <Router>
        <div className="min-h-screen bg-white dark:bg-primary-900 text-primary-900 dark:text-white transition-colors duration-200">
          <Navbar />
          <main className="bg-white dark:bg-primary-900 transition-colors duration-200">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/information" element={<Information />} />
              <Route path="/studio-policies" element={<StudioPolicies />} />
              <Route path="/studio-calendar" element={<StudioCalendar />} />
              <Route path="/services" element={<Services />} />
              <Route path="/gallery" element={<Gallery />} />
              <Route path="/contact" element={<Contact />} />
              {/* Legacy route redirect */}
              <Route path="/about" element={<Information />} />
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;




