const Services = () => {
  return (
    <div className="min-h-screen bg-page-light dark:bg-page-dark transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-800/50 dark:to-secondary-800/50"></div>
        
        <div className="container mx-auto px-4 z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl md:text-6xl font-bold mb-6 text-text-light dark:text-text-dark">
              Our Services
            </h1>
            <p className="text-xl text-text-light dark:text-text-dark mb-8">
              Comprehensive music education for all skill levels
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-primary-50 dark:bg-primary-800 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-semibold mb-4 text-primary-900 dark:text-white">
                  {service.title}
                </h3>
                <p className="text-primary-700 dark:text-primary-200 mb-6">
                  {service.description}
                </p>
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-primary-700 dark:text-primary-300">
                      <span className="mr-2">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
                <div className="mt-auto">
                  <span className="text-2xl font-bold text-primary-900 dark:text-white">{service.price}</span>
                  <span className="text-primary-600 dark:text-primary-400 ml-1">{service.period}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

const services = [
  {
    icon: "🎹",
    title: "Piano Lessons",
    description: "One-on-one instruction tailored to your goals and pace",
    features: [
      "Weekly 30, 45, or 60-minute sessions",
      "Personalized curriculum",
      "Progress tracking",
      "Flexible scheduling"
    ],
    price: "$60",
    period: "per hour"
  },
  {
    icon: "📝",
    title: "Music Theory",
    description: "Build a strong foundation in music theory",
    features: [
      "Comprehensive curriculum",
      "Ear training",
      "Composition basics",
      "Regular assessments"
    ],
    price: "$45",
    period: "per hour"
  },
  {
    icon: "✨",
    title: "Coming Soon",
    description: "More programs coming soon as we expand",
    features: [
      "New instruments",
      "Group classes",
      "Ensemble opportunities",
      "Special workshops"
    ],
    price: "TBA",
    period: ""
  }
];

export default Services;








