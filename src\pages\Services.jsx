import { Link } from 'react-router-dom';
import { useState } from 'react';

const Services = () => {
  const [expandedPiano, setExpandedPiano] = useState(false);
  const [expandedSinging, setExpandedSinging] = useState(false);

  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-800/50 dark:to-secondary-800/50"></div>

        <div className="container mx-auto px-4 z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 dark:from-primary-200 dark:to-secondary-300 bg-clip-text text-transparent">
              Our Lessons
            </h1>
            <p className="text-xl text-primary-900 dark:text-white mb-8">
              Piano and Singing lessons for all skill levels
            </p>
          </div>
        </div>
      </section>

      {/* Piano Lessons */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gray-50 dark:bg-primary-800 rounded-2xl p-8 mb-8">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-primary-100 dark:bg-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-3xl">🎹</span>
                </div>
                <h2 className="text-3xl font-display font-bold mb-4 text-neutral-850 dark:text-white">
                  Piano Lessons
                </h2>
                <p className="text-lg text-neutral-600 dark:text-gray-300 mb-6">
                  Learn piano with our experienced instructors. All skill levels welcome.
                </p>
                <div className="text-center mb-6">
                  <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">$43.50</span>
                  <span className="text-neutral-600 dark:text-gray-300 ml-2">per 30-minute lesson</span>
                </div>
                <button
                  onClick={() => setExpandedPiano(!expandedPiano)}
                  className="inline-flex items-center px-6 py-3 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-lg hover:bg-primary-200 dark:hover:bg-primary-600 transition-colors duration-200"
                >
                  {expandedPiano ? 'Less Information' : 'More Information'}
                  <svg className={`w-4 h-4 ml-2 transition-transform ${expandedPiano ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>

              {expandedPiano && (
                <div className="border-t border-primary-200 dark:border-primary-600 pt-6">
                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <h4 className="font-semibold text-neutral-850 dark:text-white mb-2">30 Minutes</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Per Lesson:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$43.50</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Total (38 lessons):</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$1,653.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Monthly:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$155.30</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <h4 className="font-semibold text-neutral-850 dark:text-white mb-2">45 Minutes</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Per Lesson:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$64.50</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Total (38 lessons):</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$2,451.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Monthly:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$235.10</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <h4 className="font-semibold text-neutral-850 dark:text-white mb-2">60 Minutes</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Per Lesson:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$87.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Total (38 lessons):</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$3,306.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Monthly:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$320.60</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Singing Lessons */}
      <section className="py-16 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-primary-800 rounded-2xl p-8 mb-8">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-primary-100 dark:bg-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-3xl">🎤</span>
                </div>
                <h2 className="text-3xl font-display font-bold mb-4 text-neutral-850 dark:text-white">
                  Singing Lessons
                </h2>
                <p className="text-lg text-neutral-600 dark:text-gray-300 mb-6">
                  Coming Soon! Develop your voice with professional vocal instruction.
                </p>
                <div className="text-center mb-6">
                  <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">$43.50</span>
                  <span className="text-neutral-600 dark:text-gray-300 ml-2">per 30-minute lesson</span>
                </div>
                <button
                  onClick={() => setExpandedSinging(!expandedSinging)}
                  className="inline-flex items-center px-6 py-3 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-lg hover:bg-primary-200 dark:hover:bg-primary-600 transition-colors duration-200"
                >
                  {expandedSinging ? 'Less Information' : 'More Information'}
                  <svg className={`w-4 h-4 ml-2 transition-transform ${expandedSinging ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>

              {expandedSinging && (
                <div className="border-t border-primary-200 dark:border-primary-600 pt-6">
                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <h4 className="font-semibold text-neutral-850 dark:text-white mb-2">30 Minutes</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Per Lesson:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$43.50</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Total (38 lessons):</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$1,653.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Monthly:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$155.30</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <h4 className="font-semibold text-neutral-850 dark:text-white mb-2">45 Minutes</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Per Lesson:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$64.50</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Total (38 lessons):</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$2,451.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Monthly:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$235.10</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <h4 className="font-semibold text-neutral-850 dark:text-white mb-2">60 Minutes</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Per Lesson:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$87.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Total (38 lessons):</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$3,306.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600 dark:text-gray-300">Monthly:</span>
                          <span className="font-semibold text-neutral-850 dark:text-white">$320.60</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Registration Fee & Deposit */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-primary-50 dark:bg-primary-800/50 rounded-xl p-8 text-center mb-8">
              <h3 className="text-2xl font-semibold mb-4 text-neutral-850 dark:text-white">
                Registration Fee & Deposit
              </h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-lg mb-2 text-neutral-850 dark:text-white">Registration Fee</h4>
                  <p className="text-neutral-600 dark:text-gray-300">
                    <span className="font-bold text-xl text-primary-600 dark:text-primary-400">$50 per family</span><br />
                    <span className="text-sm">(non-refundable, covers admin costs)</span>
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-lg mb-2 text-neutral-850 dark:text-white">Deposit</h4>
                  <p className="text-neutral-600 dark:text-gray-300">
                    <span className="font-bold text-xl text-primary-600 dark:text-primary-400">$100</span><br />
                    <span className="text-sm">(applied toward tuition, secures your spot)</span>
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center">
              <Link to="/contact">
                <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200 text-lg">
                  Get Started Today
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;







