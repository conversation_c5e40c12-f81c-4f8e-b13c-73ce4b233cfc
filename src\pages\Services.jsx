const Services = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-800/50 dark:to-secondary-800/50"></div>
        
        <div className="container mx-auto px-4 z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 dark:from-primary-200 dark:to-secondary-300 bg-clip-text text-transparent">
              What We Offer
            </h1>
            <p className="text-xl text-primary-900 dark:text-white mb-8">
              Comprehensive music education for all skill levels
            </p>
          </div>
        </div>
      </section>

      {/* Tuition Information */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-display font-bold text-center mb-4 text-neutral-850 dark:text-white">
              Tuition
            </h2>
            <p className="text-center text-neutral-600 dark:text-gray-300 mb-12">
              School Year (10 months - 38 lessons, September 2025 - June 2026)
            </p>

            {/* Lesson Options */}
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              {lessonOptions.map((option, index) => (
                <div key={index} className="bg-gray-50 dark:bg-primary-800 rounded-xl p-8 shadow-sm hover:shadow-lg transition-shadow duration-200">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-primary-100 dark:bg-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
                      <span className="text-2xl">🎹</span>
                    </div>
                    <h3 className="text-2xl font-bold mb-4 text-neutral-850 dark:text-white">
                      {option.duration}
                    </h3>
                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between items-center">
                        <span className="text-neutral-600 dark:text-gray-300">Per Lesson:</span>
                        <span className="font-semibold text-neutral-850 dark:text-white">${option.perLesson}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-neutral-600 dark:text-gray-300">Total:</span>
                        <span className="font-semibold text-neutral-850 dark:text-white">${option.total}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-neutral-600 dark:text-gray-300">Monthly:</span>
                        <span className="font-semibold text-neutral-850 dark:text-white">${option.monthly}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-neutral-600 dark:text-gray-300">Deposit:</span>
                        <span className="font-semibold text-neutral-850 dark:text-white">${option.deposit}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Registration Fee */}
            <div className="bg-primary-50 dark:bg-primary-800/50 rounded-xl p-6 text-center">
              <h3 className="text-xl font-semibold mb-2 text-neutral-850 dark:text-white">
                Registration Fee
              </h3>
              <p className="text-neutral-600 dark:text-gray-300">
                <span className="font-bold text-lg text-primary-600 dark:text-primary-400">$50 per family</span> (non-refundable)
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

const lessonOptions = [
  {
    duration: "30 minutes",
    perLesson: "43.50",
    total: "1,653.00",
    monthly: "155.30",
    deposit: "100"
  },
  {
    duration: "45 minutes",
    perLesson: "64.50",
    total: "2,451.00",
    monthly: "235.10",
    deposit: "100"
  },
  {
    duration: "60 minutes",
    perLesson: "87.00",
    total: "3,306.00",
    monthly: "320.60",
    deposit: "100"
  }
];

export default Services;







