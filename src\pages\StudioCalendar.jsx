import jsPDF from 'jspdf';

const StudioCalendar = () => {
  const downloadPDF = () => {
    const doc = new jsPDF();

    // Set up the document
    doc.setFontSize(20);
    doc.setFont(undefined, 'bold');
    doc.text('Riverdale School of Music', 20, 30);

    doc.setFontSize(16);
    doc.text('Studio Calendar 2025-2026', 20, 45);

    doc.setFontSize(12);
    doc.setFont(undefined, 'normal');
    doc.text('School Year Important Dates', 20, 55);

    // Add a line separator
    doc.line(20, 65, 190, 65);

    let yPosition = 80;

    // 2025 Events
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('2025', 20, yPosition);
    yPosition += 15;

    doc.setFontSize(11);
    doc.setFont(undefined, 'normal');

    const events2025 = [
      { date: 'September 2', description: 'Private lessons begin (10 months / 39 lessons)' },
      { date: 'October 13', description: 'Thanksgiving Monday - Private lessons take place as usual' },
      { date: 'December 22 - January 4, 2026', description: 'No private lessons (school is closed)' }
    ];

    events2025.forEach(event => {
      doc.setFont(undefined, 'bold');
      doc.text(event.date, 25, yPosition);
      doc.setFont(undefined, 'normal');
      doc.text(event.description, 25, yPosition + 8);
      yPosition += 20;
    });

    yPosition += 10;

    // 2026 Events
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('2026', 20, yPosition);
    yPosition += 15;

    doc.setFontSize(11);
    doc.setFont(undefined, 'normal');

    const events2026 = [
      { date: 'January 5', description: 'Private lessons recommence' },
      { date: 'February 7-8', description: 'Winter Recital' },
      { date: 'February 16', description: 'Family Day – Private lessons take place as usual' },
      { date: 'March 15-21', description: 'March Break – School will be closed. No Private lessons' },
      { date: 'March 22', description: 'Private lessons recommence' },
      { date: 'April 3-6', description: 'Easter Weekend – Private lessons take place as usual' },
      { date: 'June 6', description: 'Spring Recital' },
      { date: 'June 22', description: 'Last day of Private lessons' }
    ];

    events2026.forEach(event => {
      // Check if we need a new page
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 30;
      }

      doc.setFont(undefined, 'bold');
      doc.text(event.date, 25, yPosition);
      doc.setFont(undefined, 'normal');
      doc.text(event.description, 25, yPosition + 8);
      yPosition += 20;
    });

    // Add footer
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.setFont(undefined, 'normal');
      doc.text('Riverdale School of Music - 300 Danforth Ave, Toronto, ON', 20, 280);
      doc.text(`Page ${i} of ${pageCount}`, 170, 280);
    }

    // Save the PDF
    doc.save('RSOM-Studio-Calendar-2025-2026.pdf');
  };
  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Header Section */}
      <section className="py-16 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-display font-bold mb-4 text-neutral-850 dark:text-white">
              Studio Calendar
            </h1>
            <p className="text-lg text-neutral-600 dark:text-gray-300 mb-6">
              2025-2026 School Year Important Dates
            </p>
            <button
              onClick={downloadPDF}
              className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download PDF
            </button>
          </div>
        </div>
      </section>

      {/* Calendar Content */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            
            {/* 2025 Events */}
            <div className="mb-12">
              <h2 className="text-3xl font-display font-bold mb-8 text-center text-neutral-850 dark:text-white">
                2025
              </h2>
              <div className="space-y-6">
                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        September 2
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Private lessons begin (10 months / 39 lessons)
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-full text-sm font-medium">
                        School Year Start
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        October 13
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Thanksgiving Monday - Private lessons take place as usual
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-secondary-100 dark:bg-secondary-700 text-secondary-700 dark:text-secondary-200 rounded-full text-sm font-medium">
                        Lessons Continue
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        December 22 - January 4, 2026
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        No private lessons (school is closed)
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-accent-100 dark:bg-accent-700 text-accent-700 dark:text-accent-200 rounded-full text-sm font-medium">
                        Winter Break
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 2026 Events */}
            <div>
              <h2 className="text-3xl font-display font-bold mb-8 text-center text-neutral-850 dark:text-white">
                2026
              </h2>
              <div className="space-y-6">
                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        January 5
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Private lessons recommence
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-full text-sm font-medium">
                        Lessons Resume
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-800 dark:to-secondary-800 rounded-xl p-6 border-2 border-primary-200 dark:border-primary-600">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        February 7-8
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Winter Recital
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-600 text-white rounded-full text-sm font-medium">
                        🎵 Recital
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        February 16
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Family Day – Private lessons take place as usual
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-secondary-100 dark:bg-secondary-700 text-secondary-700 dark:text-secondary-200 rounded-full text-sm font-medium">
                        Lessons Continue
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        March 15-21
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        March Break – School will be closed. No Private lessons
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-accent-100 dark:bg-accent-700 text-accent-700 dark:text-accent-200 rounded-full text-sm font-medium">
                        Spring Break
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        March 22
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Private lessons recommence
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-full text-sm font-medium">
                        Lessons Resume
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        April 3-6
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Easter Weekend – Private lessons take place as usual
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-secondary-100 dark:bg-secondary-700 text-secondary-700 dark:text-secondary-200 rounded-full text-sm font-medium">
                        Lessons Continue
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-800 dark:to-secondary-800 rounded-xl p-6 border-2 border-primary-200 dark:border-primary-600">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        June 6
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Spring Recital
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-600 text-white rounded-full text-sm font-medium">
                        🎵 Recital
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        June 22
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Last day of Private lessons
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-accent-100 dark:bg-accent-700 text-accent-700 dark:text-accent-200 rounded-full text-sm font-medium">
                        School Year End
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>
    </div>
  );
};

export default StudioCalendar;
