const StudioCalendar = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Header Section */}
      <section className="py-16 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-display font-bold mb-4 text-neutral-850 dark:text-white">
              Studio Calendar
            </h1>
            <p className="text-lg text-neutral-600 dark:text-gray-300">
              2025-2026 School Year Important Dates
            </p>
          </div>
        </div>
      </section>

      {/* Calendar Content */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            
            {/* 2025 Events */}
            <div className="mb-12">
              <h2 className="text-3xl font-display font-bold mb-8 text-center text-neutral-850 dark:text-white">
                2025
              </h2>
              <div className="space-y-6">
                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        September 2
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Private lessons begin (10 months / 39 lessons)
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-full text-sm font-medium">
                        School Year Start
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        October 13
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Thanksgiving Monday - Private lessons take place as usual
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-secondary-100 dark:bg-secondary-700 text-secondary-700 dark:text-secondary-200 rounded-full text-sm font-medium">
                        Lessons Continue
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        December 22 - January 4, 2026
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        No private lessons (school is closed)
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-accent-100 dark:bg-accent-700 text-accent-700 dark:text-accent-200 rounded-full text-sm font-medium">
                        Winter Break
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 2026 Events */}
            <div>
              <h2 className="text-3xl font-display font-bold mb-8 text-center text-neutral-850 dark:text-white">
                2026
              </h2>
              <div className="space-y-6">
                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        January 5
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Private lessons recommence
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-full text-sm font-medium">
                        Lessons Resume
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-800 dark:to-secondary-800 rounded-xl p-6 border-2 border-primary-200 dark:border-primary-600">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        February 7-8
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Winter Recital
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-600 text-white rounded-full text-sm font-medium">
                        🎵 Recital
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        February 16
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Family Day – Private lessons take place as usual
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-secondary-100 dark:bg-secondary-700 text-secondary-700 dark:text-secondary-200 rounded-full text-sm font-medium">
                        Lessons Continue
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        March 15-21
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        March Break – School will be closed. No Private lessons
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-accent-100 dark:bg-accent-700 text-accent-700 dark:text-accent-200 rounded-full text-sm font-medium">
                        Spring Break
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        March 22
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Private lessons recommence
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-full text-sm font-medium">
                        Lessons Resume
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        April 3-6
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Easter Weekend – Private lessons take place as usual
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-secondary-100 dark:bg-secondary-700 text-secondary-700 dark:text-secondary-200 rounded-full text-sm font-medium">
                        Lessons Continue
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-800 dark:to-secondary-800 rounded-xl p-6 border-2 border-primary-200 dark:border-primary-600">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        June 6
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Spring Recital
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-primary-600 text-white rounded-full text-sm font-medium">
                        🎵 Recital
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-primary-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-primary-600 dark:text-primary-400">
                        June 22
                      </h3>
                      <p className="text-neutral-600 dark:text-gray-300">
                        Last day of Private lessons
                      </p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <span className="inline-block px-3 py-1 bg-accent-100 dark:bg-accent-700 text-accent-700 dark:text-accent-200 rounded-full text-sm font-medium">
                        School Year End
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>
    </div>
  );
};

export default StudioCalendar;
