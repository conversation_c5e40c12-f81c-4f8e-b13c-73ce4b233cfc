const Gallery = () => {
  return (
    <div className="min-h-screen bg-page-light dark:bg-page-dark transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-primary-100/20 dark:from-page-dark dark:to-primary-800/30"></div>
        
        <div className="container mx-auto px-4 z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl md:text-6xl font-bold mb-6 text-text-light dark:text-text-dark">
              Gallery
            </h1>
            <p className="text-xl text-text-light dark:text-text-dark mb-8">
              Moments and memories from our musical community
            </p>
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16 bg-page-light dark:bg-page-dark transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Gallery items */}
            {images.map((image, index) => (
              <div key={index} className="relative overflow-hidden rounded-lg shadow-lg group">
                <img 
                  src={image.url} 
                  alt={image.alt} 
                  className="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-200"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="absolute bottom-0 left-0 right-0 p-4">
                    <h3 className="text-white text-lg font-semibold">{image.title}</h3>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

const images = [
  {
    url: "/assets/images/gallery-1.jpg",
    alt: "Piano Recital",
    title: "Piano Recital"
  },
  {
    url: "/assets/images/gallery-2.jpg",
    alt: "Guitar Workshop",
    title: "Guitar Workshop"
  },
  {
    url: "/assets/images/gallery-3.jpg",
    alt: "Orchestra Practice",
    title: "Orchestra Practice"
  },
  {
    url: "/assets/images/gallery-4.jpg",
    alt: "Vocal Training",
    title: "Vocal Training"
  },
  {
    url: "/assets/images/gallery-5.jpg",
    alt: "Music Theory Class",
    title: "Music Theory Class"
  },
  {
    url: "/assets/images/gallery-6.jpg",
    alt: "Student Performance",
    title: "Student Performance"
  }
];

export default Gallery;






