import { useState, useRef } from 'react';
import emailjs from '@emailjs/browser';

const SingingPreRegistration = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const form = useRef();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await emailjs.sendForm(
        'service_your_service_id', // Replace with your EmailJS service ID
        'template_your_template_id', // Replace with your EmailJS template ID
        form.current,
        'your_public_key' // Replace with your EmailJS public key
      );
      setIsSubmitted(true);
    } catch (error) {
      console.error('Error sending email:', error);
      alert('Sorry, there was an error submitting your pre-registration. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const SuccessMessage = () => (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-primary-100 dark:bg-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
        <svg className="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
      </div>
      <h3 className="text-2xl font-bold mb-4 text-neutral-850 dark:text-white">Thank You for Your Interest!</h3>
      <p className="text-neutral-600 dark:text-gray-300 mb-6">
        We've received your pre-registration for singing lessons. We'll be in touch soon with more details about our upcoming vocal program!
      </p>
      <button
        onClick={() => setIsSubmitted(false)}
        className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium"
      >
        Submit Another Pre-Registration
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Header Section */}
      <section className="py-16 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-display font-bold mb-4 text-neutral-850 dark:text-white">
              Singing Lessons
            </h1>
            <p className="text-lg text-neutral-600 dark:text-gray-300 mb-6">
              Coming Soon - Pre-Register Now!
            </p>
            <div className="bg-primary-100 dark:bg-primary-800 rounded-lg p-6 max-w-2xl mx-auto">
              <p className="text-neutral-700 dark:text-gray-300">
                We're excited to announce that singing lessons will be available soon at Riverdale School of Music! 
                Pre-register below to be among the first to know when our vocal program launches.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pre-Registration Form */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <div className="bg-gray-50 dark:bg-primary-800 rounded-2xl p-8 shadow-sm">
              <h2 className="text-2xl font-display font-bold mb-6 text-center text-neutral-850 dark:text-white">
                Pre-Register for Singing Lessons
              </h2>
              
              {isSubmitted ? (
                <SuccessMessage />
              ) : (
                <form ref={form} onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                        Student Name *
                      </label>
                      <input
                        type="text"
                        name="student_name"
                        className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                        Student Age
                      </label>
                      <input
                        type="number"
                        name="student_age"
                        min="5"
                        max="100"
                        className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                        Parent/Guardian Name *
                      </label>
                      <input
                        type="text"
                        name="parent_name"
                        className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        name="email"
                        className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                      Previous Singing Experience
                    </label>
                    <select
                      name="experience"
                      className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">Select experience level</option>
                      <option value="beginner">Complete Beginner</option>
                      <option value="some">Some Experience</option>
                      <option value="intermediate">Intermediate</option>
                      <option value="advanced">Advanced</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                      Preferred Lesson Length
                    </label>
                    <div className="grid grid-cols-3 gap-4">
                      <label className="flex items-center">
                        <input type="radio" name="lesson_length" value="30" className="mr-2" />
                        <span className="text-neutral-700 dark:text-gray-300">30 minutes</span>
                      </label>
                      <label className="flex items-center">
                        <input type="radio" name="lesson_length" value="45" className="mr-2" />
                        <span className="text-neutral-700 dark:text-gray-300">45 minutes</span>
                      </label>
                      <label className="flex items-center">
                        <input type="radio" name="lesson_length" value="60" className="mr-2" />
                        <span className="text-neutral-700 dark:text-gray-300">60 minutes</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                      Additional Comments or Questions
                    </label>
                    <textarea
                      name="comments"
                      rows="4"
                      className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Tell us about your musical goals, preferred styles, or any questions you have..."
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white py-4 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Submitting...
                      </>
                    ) : (
                      'Pre-Register for Singing Lessons'
                    )}
                  </button>

                  <p className="text-sm text-neutral-500 dark:text-gray-400 text-center">
                    * Required fields. We'll contact you when singing lessons become available.
                  </p>
                </form>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Information Section */}
      <section className="py-16 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-display font-bold text-center mb-12 text-neutral-850 dark:text-white">
              What to Expect
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white dark:bg-primary-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 text-neutral-850 dark:text-white">
                  🎤 Vocal Technique
                </h3>
                <p className="text-neutral-600 dark:text-gray-300">
                  Learn proper breathing, posture, and vocal exercises to develop a strong, healthy singing voice.
                </p>
              </div>
              <div className="bg-white dark:bg-primary-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 text-neutral-850 dark:text-white">
                  🎵 Song Repertoire
                </h3>
                <p className="text-neutral-600 dark:text-gray-300">
                  Explore various musical styles and build a diverse repertoire of songs you love to sing.
                </p>
              </div>
              <div className="bg-white dark:bg-primary-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 text-neutral-850 dark:text-white">
                  🎭 Performance Skills
                </h3>
                <p className="text-neutral-600 dark:text-gray-300">
                  Build confidence and stage presence through performance opportunities and recitals.
                </p>
              </div>
              <div className="bg-white dark:bg-primary-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 text-neutral-850 dark:text-white">
                  👨‍🏫 Expert Instruction
                </h3>
                <p className="text-neutral-600 dark:text-gray-300">
                  Learn from qualified vocal instructors with experience in various musical genres.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SingingPreRegistration;
