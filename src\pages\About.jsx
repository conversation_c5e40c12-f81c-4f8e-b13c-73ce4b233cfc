const About = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Story Section */}
      <section className="py-20 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Introduction */}
            <div className="text-center mb-16">
              <div className="w-24 h-24 bg-primary-100 dark:bg-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-4xl">🎹</span>
              </div>
              <h2 className="font-display text-3xl md:text-4xl font-bold mb-4 text-neutral-850 dark:text-white">
                Meet Becky
              </h2>
              <p className="text-lg text-primary-600 dark:text-primary-400 font-medium">
                Founder & Director of Riverdale School of Music
              </p>
            </div>

            {/* Story Content */}
            <div className="bg-white dark:bg-primary-800 rounded-xl p-8 md:p-12 shadow-sm mb-12">
              <div className="prose prose-lg dark:prose-invert max-w-none">
                <p className="text-neutral-600 dark:text-gray-300 leading-relaxed text-lg mb-6">
                  Hello and welcome to Riverdale School of Music! I'm Becky, the founder and director of RSOM. I've been playing the piano since the age of 7 and went on to attend Wexford school for the arts where I studied drama, musical theatre and stage band.
                </p>
                <p className="text-neutral-600 dark:text-gray-300 leading-relaxed text-lg mb-6">
                  After earning my Bachelor of Music Education from the University of Toronto in 2019, I spent several years teaching at various music schools and offering private lessons right here in the neighborhood. Along the way, I had the privilege of meeting so many wonderful families—it's truly where I fell in love with teaching piano.
                </p>
                <p className="text-neutral-600 dark:text-gray-300 leading-relaxed text-lg">
                  Fast forward six years, and I'm now incredibly proud to run my own music school, offering both private lessons and early childhood music classes in the very community that inspired it all.
                </p>
              </div>
            </div>

            {/* Teaching Philosophy */}
            <div className="bg-white dark:bg-primary-800 rounded-xl p-8 md:p-12 shadow-sm">
              <div className="prose prose-lg dark:prose-invert max-w-none">
                <h3 className="text-2xl md:text-3xl font-display font-bold mb-6 text-neutral-850 dark:text-white">
                  Teaching Philosophy
                </h3>
                <p className="text-neutral-600 dark:text-gray-300 leading-relaxed text-lg mb-6">
                  As a teacher, I believe that truly meaningful learning happens when there's a genuine connection between student and teacher. That's why I make it a priority to get to know my students beyond just their musical interests. I aim to be more than just an instructor—I'm also a mentor, a supporter, and sometimes even a friend.
                </p>
                <p className="text-neutral-600 dark:text-gray-300 leading-relaxed text-lg mb-6">
                  I care deeply about who they are as people, not just as musicians. Supporting their personal growth goes hand-in-hand with nurturing their musical journey, and I feel honored to walk alongside them as they grow in confidence, creativity, and character.
                </p>
                <p className="text-neutral-600 dark:text-gray-300 leading-relaxed text-lg">
                  At the heart of this school is a simple but powerful philosophy: music is a journey, and every journey is unique. I truly believe that music is for everyone. Whether you're working toward an RCM exam, preparing for a competition, or simply playing for the joy of it, my goal is to support you every step of the way.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="font-display text-3xl md:text-4xl font-bold text-center mb-16 text-neutral-850 dark:text-white">
              Our Values
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              {values.map((value, index) => (
                <div key={index} className="bg-gray-50 dark:bg-primary-800 p-8 rounded-xl shadow-sm text-center hover:shadow-xl transition-all duration-200">
                  <div className="w-16 h-16 bg-primary-100 dark:bg-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <span className="text-2xl">{value.icon}</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-neutral-850 dark:text-white">
                    {value.title}
                  </h3>
                  <p className="text-neutral-600 dark:text-gray-300">
                    {value.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="font-display text-3xl md:text-4xl font-bold text-center mb-16 text-neutral-850 dark:text-white">
              Our Impact
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl md:text-5xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-neutral-600 dark:text-gray-300 font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

const values = [
  {
    icon: "❤️",
    title: "Community First",
    description: "We believe music education thrives in a supportive, welcoming environment where every student feels valued and encouraged."
  },
  {
    icon: "🎯",
    title: "Personalized Learning",
    description: "Every student is unique. We tailor our teaching approach to match individual learning styles, goals, and musical interests."
  },
  {
    icon: "🌟",
    title: "Excellence & Joy",
    description: "We strive for musical excellence while ensuring that learning remains fun, engaging, and inspiring for students of all ages."
  }
];

const stats = [
  { value: "6+", label: "Years Teaching" },
  { value: "120+", label: "Happy Students" },
  { value: "50+", label: "Succesful RCM Exams" }
];

export default About;




