const About = () => {
  return (
    <div className="min-h-screen bg-page-light dark:bg-page-dark transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-800/30 dark:to-primary-900/30"></div>
        
        <div className="container mx-auto px-4 z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl md:text-6xl font-bold mb-6 text-text-light dark:text-text-dark">
              Our Story
            </h1>
            <p className="text-xl text-text-light dark:text-text-dark mb-8">
              Dedicated to musical excellence since [year]
            </p>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16 bg-page-light dark:bg-page-dark transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="prose dark:prose-invert prose-lg">
              <p className="text-text-light dark:text-text-dark">
                [Your content here]
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

const stats = [
  { value: "500+", label: "Students Taught" },
  { value: "15+", label: "Expert Teachers" },
  { value: "25+", label: "Instruments" },
  { value: "98%", label: "Student Satisfaction" }
];

const instructors = [
  {
    name: "Becky Cai",
    instrument: "Piano & Music Theory",
    description: "Professional pianist and experienced music educator passionate about nurturing students' musical journey."
  },
  {
    name: "<Future Instructor>",
    instrument: "Guitar & Bass",
    description: "Position opening soon - expanding our instrumental program."
  },
  {
    name: "<Future Instructor>",
    instrument: "Violin & Orchestra",
    description: "Position opening soon - expanding our string program."
  }
];

export default About;







