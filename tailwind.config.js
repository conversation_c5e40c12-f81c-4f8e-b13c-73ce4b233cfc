/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f8faf8',   // Very light green
          100: '#f0f4f0',  // Light green
          200: '#e1e9e1',  // Soft green
          300: '#c8d6c8',  // Light muted green
          400: '#a4b7a4',  // Medium green
          500: '#7c977c',  // Main green (#7c977c)
          600: '#6a826a',  // Slightly deeper green
          700: '#586d58',  // Medium deep green
          800: '#465846',  // Dark green
          900: '#344334',  // Very dark green
        },
        secondary: {
          50: '#f9faf9',   // Very light sage
          100: '#f3f5f3',  // Light sage
          200: '#e6eae6',  // Soft sage
          300: '#d4dbd4',  // Light muted sage
          400: '#b8c4b8',  // Medium sage
          500: '#7c977c',  // Main green (same as primary-500)
          600: '#6a826a',  // Rich sage
          700: '#586d58',  // Deep sage
          800: '#465846',  // Dark sage
          900: '#344334',  // Deepest sage
        },
        accent: {
          50: '#faf8f5',   // Cream
          100: '#f2ede4',  // Light cream
          200: '#e6d9c8',  // Warm cream
          300: '#d4c2a5',  // Muted gold
          400: '#bfa47f',  // Warm tan
          500: '#a6875c',  // Rich tan
          600: '#8b6d44',  // Deep tan
          700: '#6d5435',  // Dark tan
          800: '#4f3d28',  // Very dark tan
          900: '#33281c',  // Deepest tan
        },
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          850: '#1a1a1a',
          900: '#171717',
        },
      },
    },
  },
};