/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#faf9f7',   // Very light warm beige
          100: '#f5f3f0',  // Light warm beige
          200: '#e8e4de',  // Soft warm gray
          300: '#d4cfc5',  // Muted warm gray
          400: '#b8b0a1',  // Medium warm gray
          500: '#9c8f7c',  // Warm brown-gray
          600: '#7d6f5a',  // Rich warm brown
          700: '#5f5242',  // Deep warm brown
          800: '#453a2e',  // Dark warm brown
          900: '#2d241b',  // Very dark warm brown
        },
        secondary: {
          50: '#f7f8f6',   // Very light sage
          100: '#eef1eb',  // Light sage
          200: '#dde3d6',  // Soft sage
          300: '#c4d0b8',  // Muted sage green
          400: '#a3b594',  // Medium sage
          500: '#849972',  // Rich sage green
          600: '#6b7d56',  // Deep sage
          700: '#526142',  // Dark sage
          800: '#3d4831',  // Very dark sage
          900: '#2a3021',  // Deepest sage
        },
        accent: {
          50: '#faf8f5',   // Cream
          100: '#f2ede4',  // Light cream
          200: '#e6d9c8',  // Warm cream
          300: '#d4c2a5',  // Muted gold
          400: '#bfa47f',  // Warm tan
          500: '#a6875c',  // Rich tan
          600: '#8b6d44',  // Deep tan
          700: '#6d5435',  // Dark tan
          800: '#4f3d28',  // Very dark tan
          900: '#33281c',  // Deepest tan
        },
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          850: '#1a1a1a',
          900: '#171717',
        },
      },
    },
  },
};