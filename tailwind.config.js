/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0fdf4',   // Lightest green
          100: '#dcfce7',  // Very light green
          200: '#bbf7d0',  // Light green
          300: '#86efac',  // Soft green
          400: '#4ade80',  // Medium green
          500: '#22c55e',  // Base green
          600: '#16a34a',  // Deep green
          700: '#15803d',  // Rich green
          800: '#166534',  // Dark green
          900: '#14532d',  // Darkest green
        },
        secondary: {
          50: '#f0fdfa',   // Lightest teal
          100: '#ccfbf1',  // Very light teal
          200: '#99f6e4',  // Light teal
          300: '#5eead4',  // Soft teal
          400: '#2dd4bf',  // Medium teal
          500: '#14b8a6',  // Base teal
          600: '#0d9488',  // Deep teal
          700: '#0f766e',  // Rich teal
          800: '#115e59',  // Dark teal
          900: '#134e4a',  // Darkest teal
        },
        accent: {
          50: '#ecfdf5',   // Lightest emerald
          100: '#d1fae5',  // Very light emerald
          200: '#a7f3d0',  // Light emerald
          300: '#6ee7b7',  // Soft emerald
          400: '#34d399',  // Medium emerald
          500: '#10b981',  // Base emerald
          600: '#059669',  // Deep emerald
          700: '#047857',  // Rich emerald
          800: '#065f46',  // Dark emerald
          900: '#064e3b',  // Darkest emerald
        },
        // Base colors for light/dark mode
        page: {
          light: '#ffffff',    // Light mode background
          dark: '#0f172a',     // Dark mode background (slate-900)
        },
        text: {
          light: '#0f172a',    // Light mode text (slate-900)
          dark: '#f8fafc',     // Dark mode text (slate-50)
        }
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        display: ['Plus Jakarta Sans', 'sans-serif'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
      }
    },
  },
  plugins: [],
}










