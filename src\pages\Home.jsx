import { Link } from 'react-router-dom';

const Home = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative h-[90vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-800/50 dark:to-secondary-800/50"></div>
        
        <div className="container mx-auto px-4 z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 dark:from-primary-200 dark:to-secondary-300 bg-clip-text text-transparent">
              Discover Your Musical Journey
            </h1>
            <p className="text-xl md:text-2xl text-primary-900 dark:text-white mb-12">
              Where passion meets excellence in music education
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact">
                <button className="w-full sm:w-auto bg-secondary-600 hover:bg-secondary-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200">
                  Start Learning Today
                </button>
              </Link>
              <Link to="/programs">
                <button className="w-full sm:w-auto bg-white dark:bg-primary-800 text-secondary-600 dark:text-secondary-400 px-8 py-4 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-primary-700 transition-colors duration-200">
                  Explore Programs
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-display font-bold text-center mb-16 text-neutral-850 dark:text-white">
            Why Choose Us
          </h2>
          <div className="grid md:grid-cols-3 gap-12">
            {features.map((feature, index) => (
              <div key={index} className="bg-gray-50 dark:bg-primary-800 p-8 rounded-xl transition-transform duration-200 hover:-translate-y-1">
                <div className="mb-6">
                  <span className="text-4xl">{feature.icon}</span>
                </div>
                <h3 className="text-xl font-semibold mb-4 text-neutral-850 dark:text-white">
                  {feature.title}
                </h3>
                <p className="text-neutral-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-24 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-display font-bold text-center mb-16 text-neutral-850 dark:text-white">
            Our Programs
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {programs.map((program, index) => (
              <div key={index} className="bg-white dark:bg-primary-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-200">
                <div className="h-48 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-t-xl"></div>
                <div className="p-8">
                  <h3 className="text-xl font-semibold mb-4 text-neutral-850 dark:text-white">
                    {program.title}
                  </h3>
                  <p className="text-neutral-600 dark:text-gray-300 mb-6">
                    {program.description}
                  </p>
                  <Link to={`/programs/${program.title.toLowerCase().replace(/\s+/g, '-')}`}>
                    <button className="text-secondary-600 dark:text-secondary-400 font-medium hover:text-secondary-700 dark:hover:text-secondary-300 transition-colors duration-200">
                      Learn More →
                    </button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-display font-bold text-center mb-16 text-neutral-850 dark:text-white">
            Student Success Stories
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white dark:bg-primary-800 p-8 rounded-xl shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-700 rounded-full flex items-center justify-center">
                    <span className="text-xl">{testimonial.icon}</span>
                  </div>
                  <div className="ml-4">
                    <h3 className="font-semibold text-lg text-neutral-850 dark:text-white">{testimonial.name}</h3>
                    <p className="text-sm text-neutral-600 dark:text-gray-300">{testimonial.program}</p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-gray-300 italic">"{testimonial.quote}"</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-24 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-4xl font-display font-bold mb-6 text-neutral-850 dark:text-white">
              Ready to Begin Your Musical Journey?
            </h2>
            <p className="text-xl text-neutral-600 dark:text-gray-300 mb-8">
              Join our community of musicians and start learning today.
            </p>
            <Link to="/contact">
              <button className="bg-secondary-600 hover:bg-secondary-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200">
                Schedule a Free Consultation
              </button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

const features = [
  {
    icon: "🎵",
    title: "Expert Instructors",
    description: "Learn from passionate professionals with years of teaching and performance experience."
  },
  {
    icon: "🎹",
    title: "Personalized Learning",
    description: "Tailored instruction to match your unique goals and learning style."
  },
  {
    icon: "🎸",
    title: "Modern Facilities",
    description: "State-of-the-art classrooms and practice rooms equipped with professional instruments."
  }
];

const programs = [
  {
    title: "Piano Lessons",
    description: "From classical to contemporary, develop your skills with our comprehensive piano program."
  },
  {
    title: "Guitar Mastery",
    description: "Electric or acoustic, learn techniques and styles from experienced guitarists."
  },
  {
    title: "Vocal Training",
    description: "Discover your voice and build confidence with our professional vocal coaches."
  }
];

const testimonials = [
  {
    icon: "🎹",
    name: "Sarah Chen",
    program: "Piano Performance",
    quote: "After a year of lessons, I went from being a complete beginner to performing in my first recital. The personalized attention and encouragement I received made all the difference."
  },
  {
    icon: "🎸",
    name: "Michael Rodriguez",
    program: "Guitar",
    quote: "The instructors here don't just teach you to play - they inspire you to truly understand and love music. Best decision I've made for my musical journey."
  },
  {
    icon: "🎵",
    name: "Emma Thompson",
    program: "Music Theory",
    quote: "The structured curriculum and patient teaching approach helped me grasp complex musical concepts I had struggled with for years."
  }
];

export default Home;
















