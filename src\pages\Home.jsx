import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';

const Home = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const isMobile = window.innerWidth < 768; // md breakpoint
  const cardsPerView = isMobile ? 1 : 3;

  // Add window resize listener
  useEffect(() => {
    const handleResize = () => {
      const newIsMobile = window.innerWidth < 768;
      if (newIsMobile !== isMobile) {
        setCurrentIndex(0); // Reset position when switching between mobile and desktop
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobile]);

  const nextTestimonial = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    const maxIndex = testimonials.length - (isMobile ? 1 : 3);
    const nextIndex = currentIndex + 1;

    if (nextIndex > maxIndex) {
      setCurrentIndex(0);
    } else {
      setCurrentIndex(nextIndex);
    }
  };

  const prevTestimonial = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    const maxIndex = testimonials.length - (isMobile ? 1 : 3);
    const prevIndex = currentIndex - 1;

    if (prevIndex < 0) {
      setCurrentIndex(maxIndex);
    } else {
      setCurrentIndex(prevIndex);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAnimating(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [currentIndex]);

  // Calculate the transform percentage
  const getTransformPercentage = () => {
    const percentage = (100 / (isMobile ? 1 : 3)) * currentIndex;
    return `translateX(-${percentage}%)`;
  };

  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative h-[90vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/student_cover_photo.jpeg"
            alt="Music education background"
            className="w-full h-full object-cover object-[center_60%] blur-[1px]"
          />
          <div className="absolute inset-0 bg-primary-500/20 dark:bg-primary-800/40"></div>
        </div>
        
        <div className="container mx-auto px-4 z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl md:text-7xl font-bold mb-6 text-white drop-shadow-lg">
              Your Journey Starts Here
            </h1>
            <p className="text-xl md:text-2xl text-white drop-shadow-md mb-12">
              Where passion meets excellence in music education
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact">
                <button className="w-full sm:w-auto bg-secondary-600 hover:bg-secondary-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200">
                  Start Learning Today
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-display font-bold text-center mb-16 text-neutral-850 dark:text-white">
            Why Choose Us
          </h2>
          <div className="grid md:grid-cols-3 gap-12">
            {features.map((feature, index) => (
              <div key={index} className="bg-gray-50 dark:bg-primary-800 p-8 rounded-xl transition-transform duration-200 hover:-translate-y-1">
                <div className="mb-6">
                  <span className="text-4xl">{feature.icon}</span>
                </div>
                <h3 className="text-xl font-semibold mb-4 text-neutral-850 dark:text-white">
                  {feature.title}
                </h3>
                <p className="text-neutral-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>



      {/* Testimonials Section */}
      <section className="py-24 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-display font-bold text-center mb-16 text-neutral-850 dark:text-white">
            Student Success Stories
          </h2>

          {/* Testimonials Carousel */}
          <div className="relative max-w-6xl mx-auto px-4 md:px-16">
            {/* Testimonials Container */}
            <div className="overflow-hidden mx-16 md:mx-20">
              <div
                className="flex transition-transform duration-300 ease-in-out"
                style={{ transform: getTransformPercentage() }}
              >
                {testimonials.map((testimonial, index) => (
                  <div
                    key={`${testimonial.name}-${index}`}
                    className="w-full md:w-1/3 flex-shrink-0 px-4"
                  >
                    <div className="bg-white dark:bg-primary-800 p-8 rounded-xl shadow-sm h-full">
                      <div className="flex items-center mb-6">
                        <div className="w-12 h-12 bg-primary-100 dark:bg-primary-700 rounded-full flex items-center justify-center">
                          <span className="text-xl">{testimonial.icon}</span>
                        </div>
                        <div className="ml-4">
                          <h3 className="font-semibold text-lg text-neutral-850 dark:text-white">
                            {testimonial.name}
                          </h3>
                          <p className="text-sm text-neutral-600 dark:text-gray-300">
                            {testimonial.program}
                          </p>
                        </div>
                      </div>
                      <p className="text-neutral-600 dark:text-gray-300 italic">
                        "{testimonial.quote}"
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Buttons */}
            <button
              onClick={prevTestimonial}
              disabled={isAnimating}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-primary-800 p-3 md:p-4 rounded-full shadow-lg hover:bg-gray-50 dark:hover:bg-primary-700 transition-colors"
              style={{ left: '3rem' }}
            >
              <svg className="w-6 h-6 text-primary-600 dark:text-primary-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              onClick={nextTestimonial}
              disabled={isAnimating}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-primary-800 p-3 md:p-4 rounded-full shadow-lg hover:bg-gray-50 dark:hover:bg-primary-700 transition-colors"
              style={{ right: '3rem' }}
            >
              <svg className="w-6 h-6 text-primary-600 dark:text-primary-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-24 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-4xl font-display font-bold mb-6 text-neutral-850 dark:text-white">
              Ready to Begin Your Musical Journey?
            </h2>
            <p className="text-xl text-neutral-600 dark:text-gray-300 mb-8">
              Join our community of musicians and start learning today.
            </p>
            <Link to="/contact">
              <button className="bg-secondary-600 hover:bg-secondary-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200">
                Schedule a Free Consultation
              </button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

const features = [
  {
    icon: "🎵",
    title: "Expert Instructors",
    description: "Learn from passionate professionals with years of teaching and performance experience."
  },
  {
    icon: "🎹",
    title: "Personalized Learning",
    description: "Tailored instruction to match your unique goals and learning style."
  },
  {
    icon: "🏫",
    title: "Modern Facilities",
    description: "State-of-the-art classrooms and practice rooms equipped with professional instruments."
  }
];



const testimonials = [
  {
    icon: "🎹",
    name: "Sarah Chen",
    program: "Piano Performance",
    quote: "After a year of lessons, I went from being a complete beginner to performing in my first recital. The personalized attention and encouragement I received made all the difference."
  },
  {
    icon: "🎸",
    name: "Michael Rodriguez",
    program: "Guitar",
    quote: "The instructors here don't just teach you to play - they inspire you to truly understand and love music. Best decision I've made for my musical journey."
  },
  {
    icon: "🎵",
    name: "Emma Thompson",
    program: "Music Theory",
    quote: "The structured curriculum and patient teaching approach helped me grasp complex musical concepts I had struggled with for years."
  },
  {
    icon: "🎻",
    name: "David Park",
    program: "Violin",
    quote: "The supportive environment and expert guidance helped me prepare for my conservatory auditions. I'm now pursuing my dreams thanks to Riverdale."
  },
  {
    icon: "🎺",
    name: "James Wilson",
    program: "Jazz Ensemble",
    quote: "The collaborative atmosphere and professional instructors have transformed my understanding of jazz. I've grown so much as a musician."
  }
];

export default Home;